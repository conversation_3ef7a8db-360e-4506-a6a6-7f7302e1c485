# PowerShell 启动脚本
# 设置代理并运行Steam爬虫

Write-Host "🚀 启动Steam免费商品爬虫" -ForegroundColor Green
Write-Host "=" * 50

# 设置代理环境变量
$env:HTTP_PROXY="http://127.0.0.1:7897"
$env:HTTPS_PROXY="http://127.0.0.1:7897"

Write-Host "🌐 代理设置完成:" -ForegroundColor Yellow
Write-Host "   HTTP_PROXY: $env:HTTP_PROXY"
Write-Host "   HTTPS_PROXY: $env:HTTPS_PROXY"
Write-Host ""

# 运行爬虫
Write-Host "🎯 启动爬虫程序..." -ForegroundColor Cyan
& python "Steam免费商品爬虫.py"
