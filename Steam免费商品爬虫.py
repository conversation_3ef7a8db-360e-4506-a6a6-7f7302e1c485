from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
import datetime
import queue
import time
import json
import pytz
import bs4
import sys
import os
from threading import Lock


API_URL_TEMPLATE = "https://store.steampowered.com/search/results/?query&start={pos}&count=100&infinite=1"
THREAD_CNT = 8

free_list = queue.Queue()
print_lock = Lock()
progress_counter = 0
total_pages = 0
start_time = 0
thread_status = {}  # 记录每个线程的状态


def clear_line():
    """清除当前行"""
    print('\r' + ' ' * 80 + '\r', end='', flush=True)


def print_progress(message, end='\n'):
    """线程安全的打印函数"""
    with print_lock:
        print(message, end=end, flush=True)


def print_progress_bar(current, total, prefix='进度', length=50):
    """打印进度条"""
    percent = (current / total) * 100
    filled_length = int(length * current // total)
    bar = '█' * filled_length + '░' * (length - filled_length)

    # 计算剩余时间
    if current > 0 and start_time > 0:
        elapsed_time = time.time() - start_time
        rate = current / elapsed_time
        if rate > 0:
            remaining_time = (total - current) / rate
            time_str = f" | 剩余: {remaining_time:.0f}秒"
        else:
            time_str = ""
    else:
        time_str = ""

    with print_lock:
        clear_line()
        print(f'\r{prefix}: |{bar}| {current}/{total} ({percent:.1f}%){time_str}',
              end='', flush=True)
        if current == total:
            print()  # 完成时换行


def print_thread_status():
    """显示线程状态"""
    with print_lock:
        active_threads = [status for status in thread_status.values()
                         if "正在处理" in status]
        completed_threads = [status for status in thread_status.values()
                           if "完成" in status]
        failed_threads = [status for status in thread_status.values()
                         if "失败" in status]

        status_line = f"🔄 活跃: {len(active_threads)} | ✅ 完成: {len(completed_threads)} | ❌ 失败: {len(failed_threads)}"
        print_progress(status_line)


def print_banner():
    """打印程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    Steam 免费商品爬虫                          ║
║                   Free Games Crawler                         ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def fetch_steam_json_response(url):
    """从Steam API获取JSON响应

    Args:
        url: Steam WebAPI URL

    Returns:
        dict: JSON内容
    """
    max_retries = 3
    retry_count = 0

    # 直接设置代理
    proxies = {
        'http': 'http://127.0.0.1:7897',
        'https': 'http://127.0.0.1:7897'
    }

    while retry_count < max_retries:
        try:
            with requests.get(url, timeout=10, proxies=proxies) as response:
                response.raise_for_status()
                return response.json()
        except requests.exceptions.RequestException as e:
            retry_count += 1
            print_progress(f"⚠️  网络请求失败 (重试 {retry_count}/{max_retries}): {e}")
            if retry_count < max_retries:
                time.sleep(5)
            else:
                raise
        except json.JSONDecodeError as e:
            retry_count += 1
            print_progress(f"⚠️  JSON解析失败 (重试 {retry_count}/{max_retries}): {e}")
            if retry_count < max_retries:
                time.sleep(5)
            else:
                raise


def get_free_goods(start, append_list=False):
    """提取100%折扣商品列表

    Args:
        start: 起始页面索引
        append_list: 是否将新发现的免费商品添加到最终列表

    Returns:
        int: 商品总数
    """
    global free_list, progress_counter, thread_status
    retry_time = 3

    # 记录线程状态
    import threading
    thread_id = threading.current_thread().ident
    page_num = start // 100 + 1

    while retry_time >= 0:
        try:
            # 更新线程状态
            with print_lock:
                thread_status[thread_id] = f"正在处理第 {page_num} 页"

            response_json = fetch_steam_json_response(
                API_URL_TEMPLATE.format(pos=start)
            )
            
            goods_count = response_json["total_count"]
            goods_html = response_json["results_html"]
            page_parser = bs4.BeautifulSoup(goods_html, "html.parser")
            
            full_discounts_div = page_parser.find_all(
                name="div", 
                attrs={"class": "search_discount_block", "data-discount": "100"}
            )
            
            sub_free_list = []
            for div in full_discounts_div:
                try:
                    parent_element = div.parent.parent.parent.parent
                    title_element = parent_element.find(
                        name="span", attrs={"class": "title"}
                    )
                    if title_element:
                        game_name = title_element.get_text().strip()
                        game_url = parent_element.get("href", "")
                        sub_free_list.append([game_name, game_url])
                except AttributeError:
                    continue

            if append_list:
                for sub_free in sub_free_list:
                    free_list.put(sub_free)

                with print_lock:
                    progress_counter += 1
                    # 更新线程状态
                    thread_status[thread_id] = f"第 {page_num} 页完成"

                    # 更新进度条
                    print_progress_bar(progress_counter, total_pages, '爬取进度')

                    # 显示线程状态
                    print_thread_status()

                    # 显示页面完成信息
                    print_progress(
                        f"✅ 页面 {page_num}/{total_pages} 完成，发现 {len(sub_free_list)} 个免费游戏"
                    )
                    print_progress("-" * 50)

            return goods_count
            
        except Exception as e:
            with print_lock:
                thread_status[thread_id] = f"第 {page_num} 页重试中"
            print_progress(
                f"❌ 处理页面 {page_num} 时出错，剩余重试次数: {retry_time}"
            )
            print_progress(f"   错误详情: {str(e)}")
            retry_time -= 1
            if retry_time >= 0:
                time.sleep(3)

    with print_lock:
        thread_status[thread_id] = f"第 {page_num} 页失败"
    print_progress(f"💥 页面 {page_num} 处理失败，已跳过")
    return 0


def main():
    """主函数"""
    global total_pages, start_time

    print_banner()

    # 获取商品总数
    print_progress("🔍 正在获取商品总数...")
    try:
        total_count = get_free_goods(0)
        if total_count == 0:
            print_progress("❌ 无法获取商品信息，程序退出")
            return

        total_pages = (total_count + 99) // 100  # 向上取整
        print_progress(f"📊 发现总计 {total_count} 个商品，共 {total_pages} 页")
        print_progress(f"🎯 预计需要处理 {total_pages} 个页面")

    except Exception as e:
        print_progress(f"❌ 获取商品总数失败: {e}")
        return

    # 多线程爬取
    print_progress(f"🚀 开始使用 {THREAD_CNT} 个线程进行爬取...")
    print_progress("🌐 代理设置: http://127.0.0.1:7897")
    print_progress("=" * 60)
    start_time = time.time()

    with ThreadPoolExecutor(max_workers=THREAD_CNT) as executor:
        futures = [
            executor.submit(get_free_goods, index, True)
            for index in range(0, total_count, 100)
        ]

        # 实时监控任务完成情况
        completed_count = 0
        for future in as_completed(futures):
            completed_count += 1
            try:
                future.result()  # 获取结果，主要是为了捕获异常
            except Exception as e:
                print_progress(f"❌ 任务执行失败: {e}")

            # 显示总体完成进度
            if completed_count % 5 == 0 or completed_count == len(futures):
                print_progress(f"📈 总任务进度: {completed_count}/{len(futures)} 个线程任务完成")

    crawl_time = time.time() - start_time
    print_progress("=" * 60)
    print_progress(f"🎉 爬取完成！总耗时: {crawl_time:.2f} 秒")
    print_progress(f"📈 平均速度: {total_pages/crawl_time:.1f} 页/秒")

    # 处理免费商品列表
    print_progress("🔄 正在处理和去重...")
    final_free_list = []
    free_names = set()

    total_items = free_list.qsize()
    processed_items = 0

    while not free_list.empty():
        free_item = free_list.get()
        game_name = free_item[0]
        if game_name and game_name not in free_names:
            free_names.add(game_name)
            final_free_list.append(free_item)

        processed_items += 1
        if processed_items % 10 == 0 or processed_items == total_items:
            print_progress_bar(processed_items, total_items, '去重进度')

    # 保存结果
    print_progress("\n💾 正在保存结果...")
    current_time = datetime.datetime.now(tz=pytz.timezone("Asia/Shanghai"))
    
    result_data = {
        "total_count": len(final_free_list),
        "free_list": final_free_list,
        "update_time": current_time.strftime('%Y-%m-%d %H:%M:%S'),
        "crawl_duration": f"{crawl_time:.2f}秒"
    }
    
    try:
        with open("免费商品详情.json", "w", encoding="utf-8") as fp:
            json.dump(result_data, fp, ensure_ascii=False, indent=2)
        
        print_progress("=" * 60)
        print_progress("✨ 爬取完成！结果统计:")
        print_progress(f"   📈 总商品数: {total_count:,}")
        print_progress(f"   🎮 免费游戏数: {len(final_free_list):,}")
        print_progress(f"   📊 免费游戏占比: {len(final_free_list)/total_count*100:.2f}%")
        print_progress(f"   📁 结果文件: 免费商品详情.json")
        print_progress(f"   ⏰ 更新时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print_progress(f"   ⚡ 总耗时: {crawl_time:.2f} 秒")

        # 显示部分免费游戏
        if final_free_list:
            print_progress("\n🎯 部分免费游戏预览:")
            for i, (name, _) in enumerate(final_free_list[:5]):
                print_progress(f"   {i+1}. {name}")
            if len(final_free_list) > 5:
                print_progress(f"   ... 还有 {len(final_free_list) - 5} 个游戏")

        print_progress("=" * 60)
        
    except Exception as e:
        print_progress(f"❌ 保存文件失败: {e}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_progress("\n⚠️  用户中断程序")
        sys.exit(1)
    except Exception as e:
        print_progress(f"\n💥 程序异常退出: {e}")
        sys.exit(1)
